<template lang="pug">
element-resize(
  v-if="!isSSR"
  :uid="item.uid"
  elementType="OmImage"
  :disableHeightResize="true"
  @element-resize="$emit('element-resize', $event)"
)
  .om-image(:id="`${item.uid}_align`" :style="imageStyle" :class="lowContrastClasses")
    img.om-image(v-if="editMode && !loaded" :id="item.uid" :src="imgSrc")
    a(
      v-else-if="isImageRedirect"
      :class="{ 'om-image-redirect': true, 'disable-link': inEditor }"
      :data-om-image-id="item.uid"
      :href="redirectUrl"
      :target="target"
      :data-om-settings="redirectSettings"
    )
      picture
        source(v-if="mobileUrl" :srcset="mobileUrl" media="(max-width: 576px)")
        source(:srcset="url")
        img.om-image(:id="item.uid" ref="image" :src="url" @load="onLoad")
    picture(v-else="")
      source(v-if="mobileUrl" :srcset="mobileUrl" media="(max-width: 576px)")
      source(:srcset="url")
      img.om-image(:id="item.uid" ref="image" :src="url" @load="onLoad" crossOrigin="")
    canvas#canvas
.om-image(:id="`${item.uid}_align`" :style="imageStyle" v-else="" :class="lowContrastClasses")
  img.om-image(v-if="editMode && !loaded" :id="item.uid" :src="imgSrc")
  a(
    v-else-if="isImageRedirect"
    :class="{ 'om-image-redirect': true, 'disable-link': inEditor }"
    :data-om-image-id="item.uid"
    :href="redirectUrl"
    :target="target"
    :data-om-settings="redirectSettings"
  )
    picture
      source(v-if="mobileUrl" :srcset="mobileUrl" media="(max-width: 576px)")
      source(:srcset="url")
      img.om-image(:id="item.uid" :src="url")
  picture(v-else="")
    source(v-if="mobileUrl" :srcset="mobileUrl" media="(max-width: 576px)")
    source(:srcset="url")
    img.om-image(:id="item.uid" :src="url")
</template>
<script>
  import { FastAverageColor } from 'fast-average-color';
  import tinycolor from 'tinycolor2';
  import ElementResize from '@/editor/components/visualizers/ElementResize.vue';
  import lazyImage from '@/mixins/lazyImage';
  import image from '@/mixins/image';

  const LOW_CONTRAST_THRESHOLD = 1.75;

  export default {
    components: { ElementResize },
    mixins: [lazyImage, image],
    props: ['isSSR'],
    data: () => ({
      desktopContrast: null,
      mobileContrast: null,
      lowContrastDesktop: false,
      lowContrastMobile: false,
    }),
    computed: {
      lowContrastClasses() {
        return {
          [`low-contrast-desktop-${this.lowContrastDesktop}`]: !!this.lowContrastDesktop,
          [`low-contrast-mobile-${this.lowContrastMobile}`]: !!this.lowContrastMobile,
        };
      },
    },
    watch: {
      url: {
        handler(url) {
          this.getDominantColorContrasts(url, 'desktop');
        },
        immediate: true,
      },
      mobileUrl: {
        handler(url) {
          this.getDominantColorContrasts(url, 'mobile');
        },
        immediate: true,
      },
      desktopContrast: {
        handler(contrast) {
          if (!contrast?.onWhite && !contrast?.onBlack) return;
          if (
            contrast?.onWhite < LOW_CONTRAST_THRESHOLD &&
            contrast?.onBlack > LOW_CONTRAST_THRESHOLD
          ) {
            this.lowContrastDesktop = 'white';
          } else if (
            contrast?.onWhite > LOW_CONTRAST_THRESHOLD &&
            contrast?.onBlack < LOW_CONTRAST_THRESHOLD
          ) {
            this.lowContrastDesktop = 'black';
          } else {
            this.lowContrastDesktop = false;
          }
        },
        immediate: true,
        deep: true,
      },
      mobileContrast: {
        handler(contrast) {
          if (!contrast?.onWhite && !contrast?.onBlack) return;
          if (
            contrast.onWhite < LOW_CONTRAST_THRESHOLD &&
            contrast.onBlack > LOW_CONTRAST_THRESHOLD
          ) {
            this.lowContrastMobile = 'white';
          } else if (
            contrast.onWhite > LOW_CONTRAST_THRESHOLD &&
            contrast.onBlack < LOW_CONTRAST_THRESHOLD
          ) {
            this.lowContrastMobile = 'black';
          } else {
            this.lowContrastMobile = false;
          }
        },
        immediate: true,
        deep: true,
      },
    },
    mounted() {
      if (!this.desktopUrl && !this.mobileUrl) return;
      this.getDominantColorContrasts(this.url, 'desktop');
      this.getDominantColorContrasts(this.mobileUrl, 'mobile');
    },
    methods: {
      async getDominantColorContrasts(url, type) {
        if (!url) return;

        try {
          const fac = new FastAverageColor();
          const color = await fac.getColorAsync(url, {
            algorithm: 'dominant',
            ignoredColor: [
              [255, 255, 255, 0, 64],
              [0, 0, 0, 0, 64],
            ],
          });

          this[`${type}Contrast`] = this.calculateContrast(color.hex);
          console.log('Contrast', this[`${type}Contrast`], color.hex);
        } catch (e) {
          console.log(`[AVG COLOR][${type}]`, e);
        }
      },

      calculateContrast(color) {
        const avg = tinycolor(color);

        return {
          onWhite: tinycolor.readability(avg, tinycolor('#ffffff')),
          onBlack: tinycolor.readability(avg, tinycolor('#000000')),
        };
      },
      onLoad(event) {
        this.render(event.target);
      },
      render(image) {
        console.log('image', image);
        const canvas = document.querySelector('#canvas');
        const gl = canvas.getContext('webgl');
        if (!gl) {
          console.log('no gl');
          return;
        }

        console.log('create program');
        const program = gl.createProgram();

        const vertexShader = gl.createShader(gl.VERTEX_SHADER);
        gl.shaderSource(
          vertexShader,
          `attribute vec2 a_position;
        attribute vec2 a_texCoord;

        varying vec2 v_texCoord;

        void main() {
            gl_Position = vec4(a_position, 0.0, 1.0);
            v_texCoord = a_texCoord;
        }`,
        );
        gl.compileShader(vertexShader);
        gl.attachShader(program, vertexShader);

        const fragmentShader = gl.createShader(gl.FRAGMENT_SHADER);
        gl.shaderSource(
          fragmentShader,
          `precision mediump float;
        uniform sampler2D u_texture;
        uniform bool u_invertBW;
        uniform float u_threshold;
        varying vec2 v_texCoord;

        void main() {
            vec4 color = texture2D(u_texture, v_texCoord);

            if (u_invertBW) {
                // Calculate luminance to determine if pixel is black or white
                float luminance = dot(color.rgb, vec3(0.299, 0.587, 0.114));

                // Check if color is close to black or white
                bool isBlack = luminance < u_threshold;
                bool isWhite = luminance > (1.0 - u_threshold);

                if (isBlack) {
                    gl_FragColor = vec4(1.0, 1.0, 1.0, color.a); // Make white
                } else if (isWhite) {
                    gl_FragColor = vec4(0.0, 0.0, 0.0, color.a); // Make black
                } else {
                    gl_FragColor = color; // Keep original color
                }
            } else {
                gl_FragColor = color; // No inversion
            }
        }`,
        );
        gl.compileShader(fragmentShader);
        gl.attachShader(program, fragmentShader);

        gl.linkProgram(program);
        const success = gl.getProgramParameter(program, gl.LINK_STATUS);
        if (!success) {
          console.error('program failed to link');
          return;
        }

        // look up where the vertex data needs to go.
        const positionLocation = gl.getAttribLocation(program, 'a_position');
        const texcoordLocation = gl.getAttribLocation(program, 'a_texCoord');

        // Create a buffer to put three 2d clip space points in
        const positionBuffer = gl.createBuffer();

        // Bind it to ARRAY_BUFFER (think of it as ARRAY_BUFFER = positionBuffer)
        gl.bindBuffer(gl.ARRAY_BUFFER, positionBuffer);

        // Create buffer with the same size as the image
        const x1 = 0;
        const y1 = 0;
        const x2 = x1 + image.width;
        const y2 = y1 + image.height;
        console.log('xy', x1, y1, x2, y2);
        gl.bufferData(
          gl.ARRAY_BUFFER,
          new Float32Array([x1, y1, x2, y1, x1, y2, x1, y2, x2, y1, x2, y2]),
          gl.STATIC_DRAW,
        );

        // provide texture coordinates for the rectangle.
        const texcoordBuffer = gl.createBuffer();
        gl.bindBuffer(gl.ARRAY_BUFFER, texcoordBuffer);
        gl.bufferData(
          gl.ARRAY_BUFFER,
          new Float32Array([0.0, 0.0, 1.0, 0.0, 0.0, 1.0, 0.0, 1.0, 1.0, 0.0, 1.0, 1.0]),
          gl.STATIC_DRAW,
        );

        // Create a texture.
        const texture = gl.createTexture();
        gl.bindTexture(gl.TEXTURE_2D, texture);

        // Set the parameters so we can render any size image.
        gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_S, gl.CLAMP_TO_EDGE);
        gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_T, gl.CLAMP_TO_EDGE);
        gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MIN_FILTER, gl.NEAREST);
        gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MAG_FILTER, gl.NEAREST);

        // Upload the image into the texture.
        gl.texImage2D(gl.TEXTURE_2D, 0, gl.RGBA, gl.RGBA, gl.UNSIGNED_BYTE, image);

        // lookup uniforms
        const resolutionLocation = gl.getUniformLocation(program, 'u_resolution');
        const displayWidth = gl.canvas.clientWidth;
        const displayHeight = gl.canvas.clientHeight;

        // Check if the canvas is not the same size.
        const needResize = gl.canvas.width !== displayWidth || gl.canvas.height !== displayHeight;

        if (needResize) {
          // Make the canvas the same size
          gl.canvas.width = displayWidth;
          gl.canvas.height = displayHeight;
        }

        // Tell WebGL how to convert from clip space to pixels
        gl.viewport(0, 0, gl.canvas.width, gl.canvas.height);

        // Clear the canvas
        gl.clearColor(0, 0, 0, 0);
        gl.clear(gl.COLOR_BUFFER_BIT);

        // Tell it to use our program (pair of shaders)
        gl.useProgram(program);

        // Turn on the position attribute
        gl.enableVertexAttribArray(positionLocation);

        // Bind the position buffer.
        gl.bindBuffer(gl.ARRAY_BUFFER, positionBuffer);

        // Tell the position attribute how to get data out of positionBuffer (ARRAY_BUFFER)
        var size = 2; // 2 components per iteration
        var type = gl.FLOAT; // the data is 32bit floats
        var normalize = false; // don't normalize the data
        var stride = 0; // 0 = move forward size * sizeof(type) each iteration to get the next position
        var offset = 0; // start at the beginning of the buffer
        gl.vertexAttribPointer(positionLocation, size, type, normalize, stride, offset);

        // Turn on the texcoord attribute
        gl.enableVertexAttribArray(texcoordLocation);

        // bind the texcoord buffer.
        gl.bindBuffer(gl.ARRAY_BUFFER, texcoordBuffer);

        // Tell the texcoord attribute how to get data out of texcoordBuffer (ARRAY_BUFFER)
        var size = 2; // 2 components per iteration
        var type = gl.FLOAT; // the data is 32bit floats
        var normalize = false; // don't normalize the data
        var stride = 0; // 0 = move forward size * sizeof(type) each iteration to get the next position
        var offset = 0; // start at the beginning of the buffer
        gl.vertexAttribPointer(texcoordLocation, size, type, normalize, stride, offset);

        // set the resolution
        gl.uniform2f(resolutionLocation, gl.canvas.width, gl.canvas.height);

        // Draw the rectangle.
        const primitiveType = gl.TRIANGLES;
        var offset = 0;
        const count = 6;
        gl.drawArrays(primitiveType, offset, count);

        gl.
      },
    },
  };
</script>

<style lang="sass">
  .om-image
    &.low-contrast-desktop
      &-black
        filter: drop-shadow(0px 0px 3px rgba(0, 0, 0, 0.4))
      &-white
        filter: drop-shadow(0px 0px 3px rgba(0, 0, 0, 0.4))
    @media screen and (max-width: 576px)
      &.low-contrast-mobile
        &-black
          filter: drop-shadow(0px 0px 3px rgba(255, 255, 255, 0.4))
        &-white
          filter: drop-shadow(0px 0px 3px rgba(0, 0, 0, 0.4))
</style>
