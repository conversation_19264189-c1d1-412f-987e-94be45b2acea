<template lang="pug">
element-resize(
  v-if="!isSSR"
  :uid="item.uid"
  elementType="OmImage"
  :disableHeightResize="true"
  @element-resize="$emit('element-resize', $event)"
)
  .om-image(:id="`${item.uid}_align`" :style="imageStyle" :class="lowContrastClasses")
    img.om-image(v-if="editMode && !loaded" :id="item.uid" :src="imgSrc")
    a(
      v-else-if="isImageRedirect"
      :class="{ 'om-image-redirect': true, 'disable-link': inEditor }"
      :data-om-image-id="item.uid"
      :href="redirectUrl"
      :target="target"
      :data-om-settings="redirectSettings"
    )
      picture
        source(v-if="mobileUrl" :srcset="mobileUrl" media="(max-width: 576px)")
        source(:srcset="url")
        img.om-image(:id="item.uid" ref="image" :src="url" @load="onLoad")
    picture(v-else="")
      source(v-if="mobileUrl" :srcset="mobileUrl" media="(max-width: 576px)")
      source(:srcset="url")
      img.om-image(:id="item.uid" ref="image" :src="url" @load="onLoad" crossOrigin="")
    canvas(:ref="`canvas_${item.uid}`" :id="`canvas_${item.uid}`" :style="canvasStyle")
.om-image(:id="`${item.uid}_align`" :style="imageStyle" v-else="" :class="lowContrastClasses")
  img.om-image(v-if="editMode && !loaded" :id="item.uid" :src="imgSrc")
  a(
    v-else-if="isImageRedirect"
    :class="{ 'om-image-redirect': true, 'disable-link': inEditor }"
    :data-om-image-id="item.uid"
    :href="redirectUrl"
    :target="target"
    :data-om-settings="redirectSettings"
  )
    picture
      source(v-if="mobileUrl" :srcset="mobileUrl" media="(max-width: 576px)")
      source(:srcset="url")
      img.om-image(:id="item.uid" :src="url")
  picture(v-else="")
    source(v-if="mobileUrl" :srcset="mobileUrl" media="(max-width: 576px)")
    source(:srcset="url")
    img.om-image(:id="item.uid" :src="url")
</template>
<script>
  import { FastAverageColor } from 'fast-average-color';
  import tinycolor from 'tinycolor2';
  import ElementResize from '@/editor/components/visualizers/ElementResize.vue';
  import lazyImage from '@/mixins/lazyImage';
  import image from '@/mixins/image';

  const LOW_CONTRAST_THRESHOLD = 1.75;

  export default {
    components: { ElementResize },
    mixins: [lazyImage, image],
    props: ['isSSR'],
    data: () => ({
      desktopContrast: null,
      mobileContrast: null,
      lowContrastDesktop: false,
      lowContrastMobile: false,
    }),
    computed: {
      lowContrastClasses() {
        return {
          [`low-contrast-desktop-${this.lowContrastDesktop}`]: !!this.lowContrastDesktop,
          [`low-contrast-mobile-${this.lowContrastMobile}`]: !!this.lowContrastMobile,
        };
      },
      canvasStyle() {
        return {
          pointerEvents: 'none',
          zIndex: 1,
          imageRendering: 'auto',
          // Ensure smooth rendering
          '-webkit-font-smoothing': 'antialiased',
          '-moz-osx-font-smoothing': 'grayscale',
        };
      },
    },
    watch: {
      url: {
        handler(url) {
          this.getDominantColorContrasts(url, 'desktop');
        },
        immediate: true,
      },
      mobileUrl: {
        handler(url) {
          this.getDominantColorContrasts(url, 'mobile');
        },
        immediate: true,
      },
      desktopContrast: {
        handler(contrast) {
          if (!contrast?.onWhite && !contrast?.onBlack) return;
          if (
            contrast?.onWhite < LOW_CONTRAST_THRESHOLD &&
            contrast?.onBlack > LOW_CONTRAST_THRESHOLD
          ) {
            this.lowContrastDesktop = 'white';
          } else if (
            contrast?.onWhite > LOW_CONTRAST_THRESHOLD &&
            contrast?.onBlack < LOW_CONTRAST_THRESHOLD
          ) {
            this.lowContrastDesktop = 'black';
          } else {
            this.lowContrastDesktop = false;
          }
        },
        immediate: true,
        deep: true,
      },
      mobileContrast: {
        handler(contrast) {
          if (!contrast?.onWhite && !contrast?.onBlack) return;
          if (
            contrast.onWhite < LOW_CONTRAST_THRESHOLD &&
            contrast.onBlack > LOW_CONTRAST_THRESHOLD
          ) {
            this.lowContrastMobile = 'white';
          } else if (
            contrast.onWhite > LOW_CONTRAST_THRESHOLD &&
            contrast.onBlack < LOW_CONTRAST_THRESHOLD
          ) {
            this.lowContrastMobile = 'black';
          } else {
            this.lowContrastMobile = false;
          }
        },
        immediate: true,
        deep: true,
      },
    },
    mounted() {
      if (!this.desktopUrl && !this.mobileUrl) return;
      this.getDominantColorContrasts(this.url, 'desktop');
      this.getDominantColorContrasts(this.mobileUrl, 'mobile');
    },
    methods: {
      async getDominantColorContrasts(url, type) {
        if (!url) return;

        try {
          const fac = new FastAverageColor();
          const color = await fac.getColorAsync(url, {
            algorithm: 'dominant',
            ignoredColor: [
              [255, 255, 255, 0, 64],
              [0, 0, 0, 0, 64],
            ],
          });

          this[`${type}Contrast`] = this.calculateContrast(color.hex);
          console.log('Contrast', this[`${type}Contrast`], color.hex);
        } catch (e) {
          console.log(`[AVG COLOR][${type}]`, e);
        }
      },

      calculateContrast(color) {
        const avg = tinycolor(color);

        return {
          onWhite: tinycolor.readability(avg, tinycolor('#ffffff')),
          onBlack: tinycolor.readability(avg, tinycolor('#000000')),
        };
      },
      onLoad(event) {
        this.render(event.target);
      },

      // Method to re-render with different inversion settings
      updateInversion(invertBW = true, threshold = 0.25) {
        const image = this.$refs.image;
        if (image) {
          this.render(image, { invertBW, threshold });
        }
      },

      // Helper method to test different threshold values
      testThreshold(threshold) {
        console.log(`Testing threshold: ${threshold}`);
        this.updateInversion(true, threshold);
      },

      // Quick presets for common threshold values
      useConservativeThreshold() {
        this.testThreshold(0.15); // Less aggressive, preserves more mid-tones
      },

      useStandardThreshold() {
        this.testThreshold(0.25); // Default balanced approach
      },

      useAggressiveThreshold() {
        this.testThreshold(0.35); // More aggressive, inverts more pixels
      },

      // Debug method to test a range of thresholds
      debugThresholds() {
        const thresholds = [0.1, 0.15, 0.2, 0.25, 0.3, 0.35, 0.4];
        let index = 0;

        const testNext = () => {
          if (index < thresholds.length) {
            console.log(
              `🎯 Testing threshold ${thresholds[index]} (${index + 1}/${thresholds.length})`,
            );
            this.testThreshold(thresholds[index]);
            index++;
            setTimeout(testNext, 2000); // Wait 2 seconds between tests
          } else {
            console.log('✅ Threshold testing complete');
          }
        };

        testNext();
      },
      render(image, options = { invertBW: true, threshold: 0.2 }) {
        console.log('Rendering image:', image);
        const canvas = this.$refs[`canvas_${this.item.uid}`];
        if (!canvas) {
          console.error('Canvas not found');
          return;
        }

        const gl = canvas.getContext('webgl', {
          antialias: true,
          alpha: true,
          premultipliedAlpha: false,
          preserveDrawingBuffer: false,
        });
        if (!gl) {
          console.error('WebGL not supported');
          return;
        }

        // Set canvas size to match the image container with device pixel ratio for crisp rendering
        const rect = image.getBoundingClientRect();
        const dpr = window.devicePixelRatio || 1;

        // Set actual canvas size in memory (scaled for device pixel ratio)
        canvas.width = rect.width * dpr;
        canvas.height = rect.height * dpr;

        // Scale canvas back down using CSS for crisp display
        canvas.style.width = `${rect.width}px`;
        canvas.style.height = `${rect.height}px`;

        console.log('Creating WebGL program');
        const program = gl.createProgram();

        const vertexShader = gl.createShader(gl.VERTEX_SHADER);
        gl.shaderSource(
          vertexShader,
          `attribute vec2 a_position;
        attribute vec2 a_texCoord;
        varying vec2 v_texCoord;

        void main() {
            gl_Position = vec4(a_position, 0.0, 1.0);
            v_texCoord = a_texCoord;
        }`,
        );
        gl.compileShader(vertexShader);

        // Check vertex shader compilation
        if (!gl.getShaderParameter(vertexShader, gl.COMPILE_STATUS)) {
          console.error('Vertex shader compilation error:', gl.getShaderInfoLog(vertexShader));
          return;
        }
        gl.attachShader(program, vertexShader);

        const fragmentShader = gl.createShader(gl.FRAGMENT_SHADER);
        gl.shaderSource(
          fragmentShader,
          `precision highp float;
        uniform sampler2D u_texture;
        uniform bool u_invertBW;
        uniform float u_threshold;
        varying vec2 v_texCoord;

        void main() {
            vec4 color = texture2D(u_texture, v_texCoord);

            if (u_invertBW) {
                // Calculate luminance using more accurate coefficients
                float luminance = dot(color.rgb, vec3(0.2126, 0.7152, 0.0722));

                // Detect black and white pixels more precisely
                float isBlack = smoothstep(u_threshold, 0.0, luminance);
                float isWhite = smoothstep(1.0 - u_threshold, 1.0, luminance);

                vec3 finalColor = color.rgb;

                // Only invert true black pixels to white
                finalColor = mix(finalColor, vec3(1.0), isBlack);

                // Only invert true white pixels to black
                finalColor = mix(finalColor, vec3(0.0), isWhite);

                gl_FragColor = vec4(finalColor, color.a);
            } else {
                gl_FragColor = color; // No inversion
            }
        }`,
        );
        gl.compileShader(fragmentShader);

        // Check fragment shader compilation
        if (!gl.getShaderParameter(fragmentShader, gl.COMPILE_STATUS)) {
          console.error('Fragment shader compilation error:', gl.getShaderInfoLog(fragmentShader));
          return;
        }
        gl.attachShader(program, fragmentShader);

        gl.linkProgram(program);
        const success = gl.getProgramParameter(program, gl.LINK_STATUS);
        if (!success) {
          console.error('Program failed to link:', gl.getProgramInfoLog(program));
          return;
        }

        // Look up attribute and uniform locations
        const positionLocation = gl.getAttribLocation(program, 'a_position');
        const texcoordLocation = gl.getAttribLocation(program, 'a_texCoord');
        const textureLocation = gl.getUniformLocation(program, 'u_texture');
        const invertBWLocation = gl.getUniformLocation(program, 'u_invertBW');
        const thresholdLocation = gl.getUniformLocation(program, 'u_threshold');

        // Create position buffer with normalized device coordinates (-1 to 1)
        const positionBuffer = gl.createBuffer();
        gl.bindBuffer(gl.ARRAY_BUFFER, positionBuffer);
        gl.bufferData(
          gl.ARRAY_BUFFER,
          new Float32Array([
            -1,
            -1, // bottom left
            1,
            -1, // bottom right
            -1,
            1, // top left
            -1,
            1, // top left
            1,
            -1, // bottom right
            1,
            1, // top right
          ]),
          gl.STATIC_DRAW,
        );

        // Create texture coordinate buffer
        const texcoordBuffer = gl.createBuffer();
        gl.bindBuffer(gl.ARRAY_BUFFER, texcoordBuffer);
        gl.bufferData(
          gl.ARRAY_BUFFER,
          new Float32Array([
            0.0,
            1.0, // bottom left
            1.0,
            1.0, // bottom right
            0.0,
            0.0, // top left
            0.0,
            0.0, // top left
            1.0,
            1.0, // bottom right
            1.0,
            0.0, // top right
          ]),
          gl.STATIC_DRAW,
        );

        // Create and setup texture with high-quality filtering
        const texture = gl.createTexture();
        gl.bindTexture(gl.TEXTURE_2D, texture);
        gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_S, gl.CLAMP_TO_EDGE);
        gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_T, gl.CLAMP_TO_EDGE);

        // Use linear filtering for smooth scaling
        gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MIN_FILTER, gl.LINEAR);
        gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MAG_FILTER, gl.LINEAR);

        // Upload the image into the texture
        gl.texImage2D(gl.TEXTURE_2D, 0, gl.RGBA, gl.RGBA, gl.UNSIGNED_BYTE, image);

        // Set viewport to match canvas size
        gl.viewport(0, 0, canvas.width, canvas.height);

        // Enable blending for better alpha handling
        gl.enable(gl.BLEND);
        gl.blendFunc(gl.SRC_ALPHA, gl.ONE_MINUS_SRC_ALPHA);

        // Clear the canvas
        gl.clearColor(0, 0, 0, 0);
        gl.clear(gl.COLOR_BUFFER_BIT);

        // Use our shader program
        gl.useProgram(program);

        // Set up position attribute
        gl.enableVertexAttribArray(positionLocation);
        gl.bindBuffer(gl.ARRAY_BUFFER, positionBuffer);
        gl.vertexAttribPointer(positionLocation, 2, gl.FLOAT, false, 0, 0);

        // Set up texture coordinate attribute
        gl.enableVertexAttribArray(texcoordLocation);
        gl.bindBuffer(gl.ARRAY_BUFFER, texcoordBuffer);
        gl.vertexAttribPointer(texcoordLocation, 2, gl.FLOAT, false, 0, 0);

        // Set uniforms
        gl.uniform1i(textureLocation, 0); // Use texture unit 0
        gl.uniform1i(invertBWLocation, options.invertBW); // Enable/disable black/white inversion
        gl.uniform1f(thresholdLocation, options.threshold); // Threshold for black/white detection

        // Bind texture to texture unit 0
        gl.activeTexture(gl.TEXTURE0);
        gl.bindTexture(gl.TEXTURE_2D, texture);

        // Draw the rectangle
        gl.drawArrays(gl.TRIANGLES, 0, 6);

        console.log('WebGL render complete', {
          invertBW: options.invertBW,
          threshold: options.threshold,
          canvasSize: { width: canvas.width, height: canvas.height },
        });
      },
    },
  };
</script>

<style lang="sass">
  .om-image
    position: relative
    &.low-contrast-desktop
      &-black
        filter: drop-shadow(0px 0px 3px rgba(0, 0, 0, 0.4))
      &-white
        filter: drop-shadow(0px 0px 3px rgba(0, 0, 0, 0.4))
    @media screen and (max-width: 576px)
      &.low-contrast-mobile
        &-black
          filter: drop-shadow(0px 0px 3px rgba(255, 255, 255, 0.4))
        &-white
          filter: drop-shadow(0px 0px 3px rgba(0, 0, 0, 0.4))
</style>
